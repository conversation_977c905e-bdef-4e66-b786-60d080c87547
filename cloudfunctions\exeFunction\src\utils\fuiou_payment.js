const crypto = require('crypto')
const axios = require('axios')
const logger = require('./logger')

/**
 * 富友支付配置模块
 * 提供富友支付API的统一接口和工具函数
 */



// 富友支付商户配置 - 生产环境参数
// 注意：这些是真实的生产环境商户信息
const FUIOU_CONFIG = {
  MERCHANT_CODE: '0004910F9046468',             // 生产环境商户号
  API_KEY: '715891b04c0611f02563d1fb416da958',  // 生产环境商户前置系统交易秘钥
  API_URL: 'https://aipay-cloud.fuioupay.com',  // 富友支付生产环境API地址
  NOTIFY_URL: 'https://cloud1-8g3yh69faf07fd82-1357079264.ap-shanghai.app.tcloudbase.com/exePaymentCallback', // 支付回调地址
  TERM_ID: '33945363',                          // 终端号（保持原有配置）
  VERSION: '1.0',                               // API版本
  ORDER_PREFIX: '18546'                         // 生产环境订单前缀
}

/**
 * 生成富友支付签名（根据官方文档）
 * @param {object} params 请求参数对象
 * @param {string} mchntKey 商户密钥
 * @returns {string} MD5签名（小写）
 */
function generateSign(params, mchntKey = FUIOU_CONFIG.API_KEY) {
  try {
    // 根据官方文档的签名格式：
    // mchnt_cd + "|" + order_type + "|" + order_amt + "|" + mchnt_order_no + "|"
    // + txn_begin_ts + "|" + goods_des + "|" + term_id + "|" + term_ip + "|"
    // + notify_url + "|" + random_str + "|" + version + "|" + mchnt_key

    const signString = [
      params.mchnt_cd || '',
      params.order_type || '',
      params.order_amt || '',
      params.mchnt_order_no || '',
      params.txn_begin_ts || '',
      params.goods_des || '',
      params.term_id || '',
      params.term_ip || '',
      params.notify_url || '',
      params.random_str || '',
      params.version || '',
      mchntKey
    ].join('|')

    logger.info('富友支付签名字符串', {
      signString: signString.replace(mchntKey, '***')
    })

    // MD5加密（小写）
    const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex')

    return sign
  } catch (error) {
    logger.error('生成富友支付签名失败', { error: error.message, params })
    throw new Error('签名生成失败')
  }
}

/**
 * 验证富友支付回调签名（根据官方文档）
 * @param {object} params 回调参数对象
 * @param {string} receivedSign 接收到的签名
 * @param {string} mchntKey 商户密钥
 * @returns {boolean} 签名是否有效
 */
function verifySign(params, receivedSign, mchntKey = FUIOU_CONFIG.API_KEY) {
  try {
    // 根据官方文档的回调签名格式：
    // mchnt_cd + "|" + mchnt_order_no + "|" + settle_order_amt + "|" + order_amt + "|"
    // + txn_fin_ts + "|" + reserved_fy_settle_dt + "|" + random_str + "|" + mchnt_key

    const signString = [
      params.mchnt_cd || '',
      params.mchnt_order_no || '',
      params.settle_order_amt || '',
      params.order_amt || '',
      params.txn_fin_ts || '',
      params.reserved_fy_settle_dt || '',
      params.random_str || '',
      mchntKey
    ].join('|')

    // MD5加密（小写）
    const expectedSign = crypto.createHash('md5').update(signString, 'utf8').digest('hex')

    const isValid = expectedSign === receivedSign.toLowerCase()

    logger.info('富友支付回调签名验证', {
      isValid,
      expectedSign,
      receivedSign: receivedSign.toLowerCase(),
      signString: signString.replace(mchntKey, '***')
    })

    return isValid
  } catch (error) {
    logger.error('验证富友支付签名失败', { error: error.message, params })
    return false
  }
}

/**
 * 生成MD5签名（查询接口专用）
 * @param {object} params 请求参数
 * @param {string} mchntKey 商户密钥
 * @returns {string} MD5签名（小写）
 */
function generateQuerySign(params, mchntKey = FUIOU_CONFIG.API_KEY) {
  try {
    // 根据官方文档的查询接口签名格式：
    // mchnt_cd + "|" + order_type + "|" + mchnt_order_no + "|" + term_id + "|"
    // + random_str + "|" + version + "|" + mchnt_key

    const signString = [
      params.mchnt_cd || '',
      params.order_type || '',
      params.mchnt_order_no || '',
      params.term_id || '',
      params.random_str || '',
      params.version || '',
      mchntKey
    ].join('|')

    logger.info('富友支付查询签名字符串', {
      signString: signString.replace(mchntKey, '***')
    })

    const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex')
    return sign
  } catch (error) {
    logger.error('生成富友支付查询签名失败', { error: error.message, params })
    throw new Error('查询签名生成失败')
  }
}

/**
 * 发送HTTP请求到富友支付API
 * @param {string} endpoint API端点
 * @param {object} data 请求数据
 * @param {object} options 请求选项
 * @returns {Promise<object>} API响应
 */
async function sendRequest(endpoint, data, options = {}) {
  try {
    const url = `${FUIOU_CONFIG.API_URL}${endpoint}`
    
    const config = {
      method: 'POST',
      url,
      data,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Numerology-AI-Chat/1.0.0',
        ...options.headers
      },
      timeout: options.timeout || 30000,
      ...options
    }
    
    logger.info('发送富友支付API请求', { 
      url, 
      method: config.method,
      dataKeys: Object.keys(data)
    })
    
    const response = await axios(config)
    
    logger.info('富友支付API响应', { 
      status: response.status,
      dataKeys: response.data ? Object.keys(response.data) : []
    })
    
    return response.data
  } catch (error) {
    logger.error('富友支付API请求失败', { 
      error: error.message,
      endpoint,
      status: error.response?.status,
      responseData: error.response?.data
    })
    
    if (error.response) {
      throw new Error(`富友支付API错误: ${error.response.status} - ${error.response.data?.message || error.message}`)
    } else if (error.request) {
      throw new Error('富友支付API网络请求失败')
    } else {
      throw new Error(`富友支付API请求配置错误: ${error.message}`)
    }
  }
}

/**
 * 生成随机字符串
 * @param {number} length 长度
 * @returns {string} 随机字符串
 */
function generateRandomString(length = 32) {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 获取客户端IP地址（模拟）
 * @returns {string} IP地址
 */
function getClientIP() {
  // 在云函数环境中，可以从请求头获取真实IP
  // 这里先使用模拟IP
  return '127.0.0.1'
}

/**
 * 统一下单接口（根据官方文档）
 * @param {object} orderInfo 订单信息
 * @param {string} orderInfo.orderNo 订单号
 * @param {number} orderInfo.amount 订单金额（分）
 * @param {string} orderInfo.description 订单描述
 * @param {string} orderInfo.goodsDescription 商品描述
 * @param {string} orderInfo.paymentMethod 支付方式（WECHAT/ALIPAY，默认WECHAT）
 * @returns {Promise<object>} 支付信息
 */
async function createUnifiedOrder(orderInfo) {
  try {
    const { orderNo, amount, description, goodsDescription, paymentMethod = 'WECHAT' } = orderInfo

    // 验证支付方式
    const validPaymentMethods = ['WECHAT', 'ALIPAY']
    if (!validPaymentMethods.includes(paymentMethod)) {
      throw new Error(`不支持的支付方式: ${paymentMethod}，支持的支付方式: ${validPaymentMethods.join(', ')}`)
    }

    // 生成随机字符串
    const randomStr = generateRandomString(32)

    // 构造请求参数（根据官方文档）
    const params = {
      version: FUIOU_CONFIG.VERSION,              // 版本号
      mchnt_cd: FUIOU_CONFIG.MERCHANT_CODE,       // 商户号
      random_str: randomStr,                      // 随机字符串
      order_type: paymentMethod,                  // 订单类型（支付方式）
      order_amt: amount.toString(),               // 订单总金额（分）
      mchnt_order_no: orderNo,                    // 商户订单号
      txn_begin_ts: new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14), // 交易起始时间
      goods_des: goodsDescription,                // 商品描述
      term_id: FUIOU_CONFIG.TERM_ID,             // 终端号
      term_ip: getClientIP(),                     // 终端IP
      notify_url: FUIOU_CONFIG.NOTIFY_URL        // 通知URL
    }

    // 生成签名
    params.sign = generateSign(params)

    logger.info('富友支付统一下单', {
      orderNo,
      amount,
      description,
      paymentMethod,
      merchantCode: FUIOU_CONFIG.MERCHANT_CODE,
      termId: FUIOU_CONFIG.TERM_ID,
      apiUrl: FUIOU_CONFIG.API_URL,
      orderType: params.order_type,
      actualTermId: params.term_id
    })

    // 发送请求
    const response = await sendRequest('/aggregatePay/preCreate', params)

    logger.info('富友支付API完整响应', { response })

    // 检查响应状态（根据官方文档）
    if (response.result_code !== '000000') {
      throw new Error(`富友支付下单失败: ${response.result_code} - ${response.result_msg || '未知错误'}`)
    }

    // 检查是否有二维码
    if (!response.qr_code) {
      throw new Error(`富友支付下单失败: 未返回二维码 - ${JSON.stringify(response)}`)
    }

    logger.info('富友支付统一下单成功', {
      orderNo,
      fuiouOrderNo: response.reserved_fy_order_no,
      qrCode: response.qr_code
    })

    return {
      success: true,
      fuiouOrderId: response.reserved_fy_trace_no || response.reserved_fy_order_no, // 使用trace_no作为订单ID
      paymentUrl: response.qr_code,
      qrCodeData: response.qr_code,
      sessionId: response.session_id,
      paymentMethod: paymentMethod, // 返回支付方式
      expireTime: new Date(Date.now() + 30 * 60 * 1000), // 30分钟过期
      rawResponse: response
    }

  } catch (error) {
    logger.error('富友支付统一下单失败', {
      error: error.message,
      orderInfo
    })
    throw error
  }
}

/**
 * 查询订单状态
 * @param {string} orderNo 订单号
 * @param {string} paymentMethod 支付方式（WECHAT/ALIPAY，默认WECHAT）
 * @returns {Promise<object>} 订单状态信息
 */
async function queryOrderStatus(orderNo, paymentMethod = 'WECHAT') {
  try {
    // 验证支付方式
    const validPaymentMethods = ['WECHAT', 'ALIPAY']
    if (!validPaymentMethods.includes(paymentMethod)) {
      throw new Error(`不支持的支付方式: ${paymentMethod}，支持的支付方式: ${validPaymentMethods.join(', ')}`)
    }

    // 生成随机字符串
    const randomStr = generateRandomString(32)

    const params = {
      version: FUIOU_CONFIG.VERSION,
      mchnt_cd: FUIOU_CONFIG.MERCHANT_CODE,
      random_str: randomStr,
      order_type: paymentMethod, // 订单类型，与下单时保持一致
      mchnt_order_no: orderNo,
      term_id: FUIOU_CONFIG.TERM_ID
    }

    // 生成查询接口专用签名
    params.sign = generateQuerySign(params)

    logger.info('查询富友支付订单状态', { orderNo, paymentMethod })

    // 发送请求
    const response = await sendRequest('/aggregatePay/commonQuery', params)

    logger.info('富友支付订单状态查询结果', {
      orderNo,
      resultCode: response.result_code,
      transactionStatus: response.trans_stat,
      orderAmount: response.order_amt
    })

    // 检查响应状态
    if (response.result_code !== '000000') {
      throw new Error(`富友支付查询失败: ${response.result_code} - ${response.result_msg || '未知错误'}`)
    }

    // 根据官方文档，trans_stat字段表示交易状态
    // SUCCESS—支付成功, NOTPAY—未支付, CLOSED—已关闭, REFUND—转入退款等
    const isPaymentSuccess = response.trans_stat === 'SUCCESS'

    return {
      success: true,
      orderStatus: isPaymentSuccess ? '02' : '01', // 转换为统一的状态码
      payStatus: isPaymentSuccess ? '02' : '01',   // 转换为统一的状态码
      orderAmount: response.order_amt,
      payAmount: response.order_amt, // 查询接口中没有单独的支付金额字段
      payTime: response.reserved_txn_fin_ts,
      payType: response.reserved_bank_type,
      transactionStatus: response.trans_stat, // 原始交易状态
      rawResponse: response
    }
    
  } catch (error) {
    logger.error('查询富友支付订单状态失败', { 
      error: error.message,
      orderNo
    })
    throw error
  }
}

/**
 * 处理富友支付回调通知
 * @param {object} callbackData 回调数据
 * @returns {object} 处理结果
 */
function processCallback(callbackData) {
  try {
    const { sign, ...params } = callbackData
    
    // 验证签名
    if (!verifySign(params, sign)) {
      throw new Error('富友支付回调签名验证失败')
    }
    
    logger.info('富友支付回调处理', { 
      orderNo: params.order_id,
      orderStatus: params.order_st,
      payAmount: params.order_pay_amt
    })
    
    return {
      success: true,
      orderNo: params.order_id,
      orderStatus: params.order_st,
      payAmount: params.order_pay_amt,
      payTime: params.txn_fin_ts,
      payType: params.pay_type,
      isPaymentSuccess: params.order_st === '02' && params.pay_st === '02',
      rawData: callbackData
    }
    
  } catch (error) {
    logger.error('处理富友支付回调失败', { 
      error: error.message,
      callbackData
    })
    throw error
  }
}

/**
 * 获取富友支付配置信息（用于调试）
 * @returns {object} 配置信息（隐藏敏感信息）
 */
function getConfig() {
  return {
    merchantCode: FUIOU_CONFIG.MERCHANT_CODE,
    apiUrl: FUIOU_CONFIG.API_URL,
    notifyUrl: FUIOU_CONFIG.NOTIFY_URL,
    termId: FUIOU_CONFIG.TERM_ID,
    version: FUIOU_CONFIG.VERSION,
    apiKey: '***' // 隐藏敏感信息
  }
}

module.exports = {
  generateSign,
  generateQuerySign,
  verifySign,
  sendRequest,
  createUnifiedOrder,
  queryOrderStatus,
  processCallback,
  getConfig,
  FUIOU_CONFIG
}
