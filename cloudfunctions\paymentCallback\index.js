const cloud = require('wx-server-sdk')
const crypto = require('crypto')
const axios = require('axios')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 富友支付商户配置 - 生产环境参数
const FUIOU_CONFIG = {
  MERCHANT_CODE: '0004910F9046468',             // 生产环境商户号
  API_KEY: '715891b04c0611f02563d1fb416da958'   // 生产环境商户前置系统交易秘钥
}

// 内部调用密钥（用于调用exeFunction云函数）
const INTERNAL_SECRET = 'numerology_ai_chat_internal_2024'

/**
 * 生成富友支付回调签名验证（根据官方文档）
 * @param {object} params 回调参数对象
 * @param {string} apiKey API密钥
 * @returns {string} MD5签名（小写）
 */
function generateCallbackSign(params, apiKey = FUIOU_CONFIG.API_KEY) {
  try {
    // 根据官方文档的回调签名格式：
    // mchnt_cd + "|" + mchnt_order_no + "|" + settle_order_amt + "|" + order_amt + "|"
    // + txn_fin_ts + "|" + reserved_fy_settle_dt + "|" + random_str + "|" + mchnt_key

    const signString = [
      params.mchnt_cd || '',
      params.mchnt_order_no || '',
      params.settle_order_amt || '',
      params.order_amt || '',
      params.txn_fin_ts || '',
      params.reserved_fy_settle_dt || '',
      params.random_str || '',
      apiKey
    ].join('|')

    console.log('富友支付回调签名字符串', {
      signString: signString.replace(apiKey, '***')
    })

    // MD5加密（小写）
    const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex')

    return sign
  } catch (error) {
    console.error('生成富友支付回调签名失败', error)
    throw new Error('签名生成失败')
  }
}

/**
 * 生成富友支付全签名验证（根据官方文档）
 * @param {object} params 回调参数对象
 * @param {string} apiKey API密钥
 * @returns {string} MD5签名（小写）
 */
function generateFullSign(params, apiKey = FUIOU_CONFIG.API_KEY) {
  try {
    // 根据官方文档的全签名格式：
    // result_code + "|" + result_msg + "|" + mchnt_cd + "|" + mchnt_order_no + "|"
    // + settle_order_amt + "|" + order_amt + "|" + txn_fin_ts + "|" + reserved_fy_settle_dt + "|"
    // + random_str + "|" + mchnt_key

    const signString = [
      params.result_code || '',
      params.result_msg || '',
      params.mchnt_cd || '',
      params.mchnt_order_no || '',
      params.settle_order_amt || '',
      params.order_amt || '',
      params.txn_fin_ts || '',
      params.reserved_fy_settle_dt || '',
      params.random_str || '',
      apiKey
    ].join('|')

    console.log('富友支付全签名字符串', {
      signString: signString.replace(apiKey, '***')
    })

    // MD5加密（小写）
    const sign = crypto.createHash('md5').update(signString, 'utf8').digest('hex')

    return sign
  } catch (error) {
    console.error('生成富友支付全签名失败', error)
    throw new Error('全签名生成失败')
  }
}

/**
 * 验证富友支付回调签名
 * @param {object} params 回调参数对象
 * @param {string} receivedSign 接收到的签名
 * @param {string} receivedFullSign 接收到的全签名（可选）
 * @returns {boolean} 签名是否有效
 */
function verifySign(params, receivedSign, receivedFullSign = null) {
  try {
    // 优先验证全签名（如果提供）
    if (receivedFullSign && params.result_code && params.result_msg) {
      const expectedFullSign = generateFullSign(params)
      const isFullSignValid = expectedFullSign === receivedFullSign.toLowerCase()

      console.log('富友支付全签名验证', {
        isValid: isFullSignValid,
        expectedFullSign,
        receivedFullSign: receivedFullSign.toLowerCase()
      })

      if (isFullSignValid) {
        return true
      }
    }

    // 验证普通签名
    const expectedSign = generateCallbackSign(params)
    const isValid = expectedSign === receivedSign.toLowerCase()

    console.log('富友支付签名验证', {
      isValid,
      expectedSign,
      receivedSign: receivedSign.toLowerCase()
    })

    return isValid
  } catch (error) {
    console.error('验证富友支付签名失败', error)
    return false
  }
}

/**
 * 调用exeFunction云函数处理支付成功
 * @param {object} paymentData 支付数据
 * @returns {Promise<boolean>} 处理是否成功
 */
async function processPaymentSuccess(paymentData) {
  try {
    const { orderNo, orderAmount, payTime, payType } = paymentData
    
    // 调用exeFunction云函数的内部接口
    const result = await cloud.callFunction({
      name: 'exeFunction',
      data: {
        action: 'processPaymentSuccess',
        orderNo,
        orderAmount,
        payTime,
        payType,
        internalSecret: INTERNAL_SECRET
      }
    })
    
    if (result.result && result.result.code === 0) {
      console.log('支付成功处理完成', { orderNo, orderAmount })
      return true
    } else {
      console.error('支付成功处理失败', result.result)
      return false
    }
  } catch (error) {
    console.error('调用exeFunction处理支付失败', error)
    return false
  }
}

/**
 * 云函数入口函数
 * 处理富友支付的异步回调通知
 */
exports.main = async (event, context) => {
  console.log('收到富友支付回调', { event, context })
  
  try {
    // 处理CORS预检请求
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Max-Age': '86400'
        },
        body: ''
      }
    }

    // 只接受POST请求
    if (event.httpMethod !== 'POST') {
      return {
        statusCode: 405,
        headers: {
          'Content-Type': 'text/plain'
        },
        body: 'Method Not Allowed'
      }
    }

    // 解析请求体
    let callbackData
    try {
      if (event.body) {
        // 检查Content-Type，根据官方文档富友支付回调使用JSON格式
        const contentType = event.headers && event.headers['Content-Type'] || event.headers && event.headers['content-type'] || ''

        if (contentType.includes('application/json')) {
          // 解析JSON格式（官方文档标准格式）
          callbackData = JSON.parse(event.body)
          console.log('解析JSON数据成功', callbackData)
        } else if (contentType.includes('application/x-www-form-urlencoded')) {
          // 兼容form-urlencoded格式
          callbackData = {}
          const params = new URLSearchParams(event.body)
          for (const [key, value] of params) {
            callbackData[key] = value
          }
          console.log('解析form-urlencoded数据成功', callbackData)
        } else {
          // 尝试解析JSON格式
          callbackData = JSON.parse(event.body)
        }
      } else {
        callbackData = event
      }
    } catch (error) {
      console.error('解析回调数据失败', error)
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'text/plain'
        },
        body: 'Invalid request body'
      }
    }

    console.log('富友支付回调数据', callbackData)

    // 提取签名和参数
    const { sign, full_sign, ...params } = callbackData

    if (!sign) {
      console.error('缺少签名参数')
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'text/plain'
        },
        body: 'Missing signature'
      }
    }

    // 验证签名（支持普通签名和全签名）
    if (!verifySign(params, sign, full_sign)) {
      console.error('富友支付回调签名验证失败')
      return {
        statusCode: 400,
        headers: {
          'Content-Type': 'text/plain'
        },
        body: 'Invalid signature'
      }
    }

    // 检查是否为支付成功回调
    // 根据官方文档，检查result_code为000000表示成功
    if (params.result_code === '000000') {
      // 支付成功，处理订单
      const paymentData = {
        orderNo: params.mchnt_order_no,
        orderAmount: params.order_amt,
        payTime: params.txn_fin_ts,
        payType: params.reserved_bank_type || 'UNKNOWN'
      }

      const processResult = await processPaymentSuccess(paymentData)

      if (processResult) {
        console.log('支付成功处理完成', paymentData)

        // 根据官方文档，返回字符串"1"表示成功
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'text/plain'
          },
          body: '1'
        }
      } else {
        console.error('支付成功处理失败', paymentData)

        // 返回失败响应，富友支付会重试
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'text/plain'
          },
          body: 'FAIL'
        }
      }
    } else {
      // 非支付成功回调，记录日志并返回成功
      console.log('收到非支付成功回调', {
        orderNo: params.mchnt_order_no,
        resultCode: params.result_code,
        resultMsg: params.result_msg
      })

      // 根据官方文档，返回字符串"1"表示成功接收
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'text/plain'
        },
        body: '1'
      }
    }

  } catch (error) {
    console.error('处理富友支付回调失败', error)
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'text/plain'
      },
      body: 'FAIL'
    }
  }
}
